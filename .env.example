# Curio API Environment Variables
# Copy this file to .env and fill in your actual values
# The .env file is ignored by git for security

# =============================================================================
# Database Configuration
# =============================================================================
DATABASE_CONNECTION_STRING=Host=localhost;Port=5432;Database=orleansdb;Username=orleans;Password=orleans123
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=orleansdb
DATABASE_USERNAME=orleans
DATABASE_PASSWORD=orleans123

# =============================================================================
# Orleans Configuration
# =============================================================================
ORLEANS_CLUSTER_ID=curio-cluster-local
ORLEANS_SERVICE_ID=curio-service-local
ORLEANS_CLUSTERING_CONNECTION_STRING=Host=localhost;Port=5432;Database=orleansdb;Username=orleans;Password=orleans123
ORLEANS_STORAGE_CONNECTION_STRING=Host=localhost;Port=5432;Database=orleansdb;Username=orleans;Password=orleans123
ORLEANS_REMINDERS_CONNECTION_STRING=Host=localhost;Port=5432;Database=orleansdb;Username=orleans;Password=orleans123

# =============================================================================
# Kafka Configuration
# =============================================================================
KAFKA_BROKERS=localhost:9092
KAFKA_CONSUMER_GROUP_ID=orleans-event-streams-local
KAFKA_TOPICS=domain-events,verification-events,user-events

# =============================================================================
# API Configuration
# =============================================================================
API_BASE_URL=https://localhost:7274
ALLOWED_HOSTS=*
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,https://localhost:7274

# =============================================================================
# Security Configuration
# =============================================================================
JWT_SECRET_KEY=your-super-secret-jwt-key-minimum-32-characters-long-for-security
JWT_ISSUER=Curio.Local
JWT_AUDIENCE=Curio.Api.Local
ENCRYPTION_KEY=your-encryption-key-32-characters
ENCRYPTION_SALT=your-salt-16-chars

# =============================================================================
# Email Configuration
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=Curio Local
EMAIL_REPLY_TO_ADDRESS=<EMAIL>
EMAIL_REPLY_TO_NAME=Curio Support

# =============================================================================
# Development/Testing Overrides
# =============================================================================
# Uncomment and modify these for local testing
# ASPNETCORE_ENVIRONMENT=Development
# ASPNETCORE_URLS=https://localhost:7274;http://localhost:5063

# =============================================================================
# Optional: Override specific settings for local development
# =============================================================================
# DATABASE_ENABLE_SENSITIVE_DATA_LOGGING=true
# EMAIL_CACHE_EXPIRATION_MINUTES=5
# ORLEANS_REFRESH_PERIOD=10
