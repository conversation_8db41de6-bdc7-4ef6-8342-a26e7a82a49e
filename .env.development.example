# Development Environment Configuration
# Copy this file to .env.development for development-specific settings

# =============================================================================
# Database Configuration (Development)
# =============================================================================
DATABASE_CONNECTION_STRING=Host=localhost;Port=5432;Database=orleansdb_dev;Username=orleans;Password=orleans123
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=orleansdb_dev
DATABASE_USERNAME=orleans
DATABASE_PASSWORD=orleans123

# =============================================================================
# Orleans Configuration (Development)
# =============================================================================
ORLEANS_CLUSTER_ID=curio-cluster-dev
ORLEANS_SERVICE_ID=curio-service-dev
ORLEANS_CLUSTERING_CONNECTION_STRING=Host=localhost;Port=5432;Database=orleansdb_dev;Username=orleans;Password=orleans123
ORLEANS_STORAGE_CONNECTION_STRING=Host=localhost;Port=5432;Database=orleansdb_dev;Username=orleans;Password=orleans123
ORLEANS_REMINDERS_CONNECTION_STRING=Host=localhost;Port=5432;Database=orleansdb_dev;Username=orleans;Password=orleans123

# =============================================================================
# Kafka Configuration (Development)
# =============================================================================
KAFKA_BROKERS=localhost:9092
KAFKA_CONSUMER_GROUP_ID=orleans-event-streams-dev
KAFKA_TOPICS=domain-events-dev,verification-events-dev,user-events-dev

# =============================================================================
# API Configuration (Development)
# =============================================================================
API_BASE_URL=https://localhost:7274
ALLOWED_HOSTS=*
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,https://localhost:7274

# =============================================================================
# Security Configuration (Development)
# =============================================================================
JWT_SECRET_KEY=development-jwt-key-minimum-32-characters-long-for-security
JWT_ISSUER=Curio.Development
JWT_AUDIENCE=Curio.Api.Development
ENCRYPTION_KEY=development-encryption-key-32-chars
ENCRYPTION_SALT=development-salt-16

# =============================================================================
# Email Configuration (Development)
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-dev-app-password
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=Curio Development
EMAIL_REPLY_TO_ADDRESS=<EMAIL>
EMAIL_REPLY_TO_NAME=Curio Dev Support

# =============================================================================
# Development-specific Settings
# =============================================================================
# Enable detailed logging
DATABASE_ENABLE_SENSITIVE_DATA_LOGGING=true

# Faster cache expiration for development
EMAIL_CACHE_EXPIRATION_MINUTES=1

# More frequent Orleans refresh for development
ORLEANS_REFRESH_PERIOD=10
