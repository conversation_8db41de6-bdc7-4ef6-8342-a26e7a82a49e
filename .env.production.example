# Production Environment Configuration
# Copy this file to .env.production for production-specific settings
# WARNING: Never commit production secrets to version control!

# =============================================================================
# Database Configuration (Production)
# =============================================================================
DATABASE_CONNECTION_STRING=Host=prod-db-server;Port=5432;Database=orleansdb_prod;Username=orleans_prod;Password=CHANGE_ME
DATABASE_HOST=prod-db-server
DATABASE_PORT=5432
DATABASE_NAME=orleansdb_prod
DATABASE_USERNAME=orleans_prod
DATABASE_PASSWORD=CHANGE_ME_SECURE_PASSWORD

# =============================================================================
# Orleans Configuration (Production)
# =============================================================================
ORLEANS_CLUSTER_ID=curio-cluster-prod
ORLEANS_SERVICE_ID=curio-service-prod
ORLEANS_CLUSTERING_CONNECTION_STRING=Host=prod-db-server;Port=5432;Database=orleansdb_prod;Username=orleans_prod;Password=CHANGE_ME
ORLEANS_STORAGE_CONNECTION_STRING=Host=prod-db-server;Port=5432;Database=orleansdb_prod;Username=orleans_prod;Password=CHANGE_ME
ORLEANS_REMINDERS_CONNECTION_STRING=Host=prod-db-server;Port=5432;Database=orleansdb_prod;Username=orleans_prod;Password=CHANGE_ME

# =============================================================================
# Kafka Configuration (Production)
# =============================================================================
KAFKA_BROKERS=kafka-prod-1:9092,kafka-prod-2:9092,kafka-prod-3:9092
KAFKA_CONSUMER_GROUP_ID=orleans-event-streams-prod
KAFKA_TOPICS=domain-events,verification-events,user-events

# =============================================================================
# API Configuration (Production)
# =============================================================================
API_BASE_URL=https://api.curio.com
ALLOWED_HOSTS=api.curio.com,curio.com
CORS_ALLOWED_ORIGINS=https://curio.com,https://app.curio.com

# =============================================================================
# Security Configuration (Production)
# =============================================================================
JWT_SECRET_KEY=CHANGE_ME_SUPER_SECURE_JWT_KEY_MINIMUM_64_CHARACTERS_LONG_FOR_PRODUCTION_USE
JWT_ISSUER=Curio.Production
JWT_AUDIENCE=Curio.Api.Production
ENCRYPTION_KEY=CHANGE_ME_SECURE_ENCRYPTION_KEY_32_CHARS
ENCRYPTION_SALT=CHANGE_ME_SECURE_SALT

# =============================================================================
# Email Configuration (Production)
# =============================================================================
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USERNAME=apikey
SMTP_PASSWORD=CHANGE_ME_SENDGRID_API_KEY
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=Curio
EMAIL_REPLY_TO_ADDRESS=<EMAIL>
EMAIL_REPLY_TO_NAME=Curio Support

# =============================================================================
# Production-specific Settings
# =============================================================================
# Disable sensitive data logging in production
DATABASE_ENABLE_SENSITIVE_DATA_LOGGING=false

# Longer cache expiration for production
EMAIL_CACHE_EXPIRATION_MINUTES=60

# Standard Orleans refresh for production
ORLEANS_REFRESH_PERIOD=30
