# Local Override Configuration
# Copy this file to .env.local for personal local overrides
# This file has the highest priority and will override all other .env files
# Perfect for personal development settings that differ from team defaults

# =============================================================================
# Personal Database Configuration
# =============================================================================
# Uncomment and modify if you use different local database settings
# DATABASE_PASSWORD=my_personal_db_password
# DATABASE_PORT=5433

# =============================================================================
# Personal Email Configuration
# =============================================================================
# Uncomment and set your personal email for testing
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=my-personal-app-password
# EMAIL_FROM_ADDRESS=<EMAIL>

# =============================================================================
# Personal Development Preferences
# =============================================================================
# Uncomment to enable/disable features for your local development
# DATABASE_ENABLE_SENSITIVE_DATA_LOGGING=true
# EMAIL_CACHE_EXPIRATION_MINUTES=0

# =============================================================================
# Personal API Configuration
# =============================================================================
# Uncomment if you want to use different ports or URLs
# API_BASE_URL=https://localhost:8080
# CORS_ALLOWED_ORIGINS=http://localhost:4200,http://localhost:8080

# =============================================================================
# Personal Security Settings
# =============================================================================
# Uncomment to use your own JWT key for local testing
# JWT_SECRET_KEY=my-personal-jwt-key-for-local-development-32-chars-minimum

# =============================================================================
# Personal Kafka Settings
# =============================================================================
# Uncomment if you use different Kafka settings locally
# KAFKA_BROKERS=localhost:9093
# KAFKA_CONSUMER_GROUP_ID=my-personal-consumer-group
