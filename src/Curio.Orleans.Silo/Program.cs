using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Orleans;
using Orleans.Configuration;
using Orleans.Hosting;
using Orleans.Streams.Kafka.Config;
using Curio.Infrastructure;
using Curio.Infrastructure.Configuration;

var builder = Host.CreateApplicationBuilder(args);

// Get configuration settings
var orleansSettings = builder.Configuration.GetSection(OrleansSettings.SectionName).Get<OrleansSettings>() ?? new OrleansSettings();
var kafkaSettings = builder.Configuration.GetSection(KafkaSettings.SectionName).Get<KafkaSettings>() ?? new KafkaSettings();

var clusteringConnectionString = builder.Configuration.GetOrleansConnectionString("clustering");
var storageConnectionString = builder.Configuration.GetOrleansConnectionString("storage");
var remindersConnectionString = builder.Configuration.GetOrleansConnectionString("reminders");
var kafkaBrokers = builder.Configuration.GetKafkaBrokersString();

// 配置Orleans Silo
builder.UseOrleans(siloBuilder =>
{
    siloBuilder
        // 集群配置 - 使用PostgreSQL clustering
        .UseAdoNetClustering(options =>
        {
            options.ConnectionString = clusteringConnectionString;
        })
        .Configure<ClusterOptions>(options =>
        {
            options.ClusterId = orleansSettings.ClusterId;
            options.ServiceId = orleansSettings.ServiceId;
        })

        // Orleans状态存储配置
        .AddAdoNetGrainStorage("Default", options =>
        {
            options.ConnectionString = storageConnectionString;
        })
        .AddAdoNetGrainStorage("JournaledGrainState", options =>
        {
            options.ConnectionString = storageConnectionString;
        })

        // JournaledGrain Event Sourcing配置
        .AddLogStorageBasedLogConsistencyProvider("EventSourcing")

        // Kafka Streams配置
        .AddKafka("KafkaStreams")
        .WithOptions(options =>
        {
            options.BrokerList = kafkaSettings.BrokerList;
            options.ConsumerGroupId = kafkaSettings.ConsumerGroupId;

            // Topics配置
            foreach (var topic in kafkaSettings.Topics)
            {
                options.AddTopic(topic);
            }
        })
        .AddJson()
        .Build()

        // PubSub存储
        .AddMemoryGrainStorage("PubSubStore")

        // Orleans Reminders（定时任务）
        .UseAdoNetReminderService(options =>
        {
            options.ConnectionString = remindersConnectionString;
        })

        // 配置日志
        .ConfigureLogging(logging => logging.AddConsole());
});

// 注册基础设施服务
builder.Services.AddInfrastructureServices(builder.Configuration);

var app = builder.Build();

// 启动Orleans Silo
Console.WriteLine("🚀 Starting Curio Orleans Silo...");
Console.WriteLine($"📊 Cluster: {orleansSettings.ClusterId}");
Console.WriteLine($"🏷️  Service: {orleansSettings.ServiceId}");
Console.WriteLine($"🗄️  Database: PostgreSQL");
Console.WriteLine($"🔗 Clustering: PostgreSQL ADO.NET");
Console.WriteLine($"📨 Kafka: {kafkaBrokers}");
Console.WriteLine($"⚡ Event Sourcing: Enabled with Kafka streaming");
Console.WriteLine();

await app.RunAsync();
