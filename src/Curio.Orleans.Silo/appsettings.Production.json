{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning", "Orleans": "Warning", "Microsoft.Orleans": "Error", "Curio.Infrastructure.Services": "Information"}}, "Application": {"Name": "Curio Orleans Silo", "Version": "1.0.0", "Environment": "Production"}, "Database": {"ConnectionString": "${DATABASE_CONNECTION_STRING}", "Host": "${DATABASE_HOST}", "Port": "${DATABASE_PORT:5432}", "Database": "${DATABASE_NAME}", "Username": "${DATABASE_USERNAME}", "Password": "${DATABASE_PASSWORD}", "CommandTimeout": 30, "MaxRetryCount": 3, "EnableSensitiveDataLogging": false}, "Orleans": {"ClusterId": "${ORLEANS_CLUSTER_ID}", "ServiceId": "${ORLEANS_SERVICE_ID}", "Clustering": {"Provider": "AdoNet", "ConnectionString": "${ORLEANS_CLUSTERING_CONNECTION_STRING}", "RefreshPeriod": 30, "DeathVoteExpirationTimeout": 120}, "Storage": {"DefaultProvider": "AdoNet", "ConnectionString": "${ORLEANS_STORAGE_CONNECTION_STRING}", "UseJsonFormat": true}, "Streaming": {"Provider": "Kafka", "ConnectionString": "${KAFKA_BROKERS}"}, "Reminders": {"Provider": "AdoNet", "ConnectionString": "${ORLEANS_REMINDERS_CONNECTION_STRING}"}}, "Kafka": {"BrokerList": ["${KAFKA_BROKERS}"], "ConsumerGroupId": "${KAFKA_CONSUMER_GROUP_ID}", "Topics": ["${KAFKA_TOPICS}"], "SessionTimeoutMs": 30000, "HeartbeatIntervalMs": 3000, "AutoOffsetReset": "earliest", "EnableAutoCommit": true, "AutoCommitIntervalMs": 5000, "MaxPollRecords": 500, "FetchMinBytes": 1, "FetchMaxWaitMs": 500}, "Email": {"Smtp": {"Host": "${SMTP_HOST}", "Port": "${SMTP_PORT:587}", "Username": "${SMTP_USERNAME}", "Password": "${SMTP_PASSWORD}", "EnableSsl": true, "TimeoutSeconds": 30, "UseDefaultCredentials": false}, "Templates": {"TemplatesDirectory": "EmailTemplates", "EnableCaching": true, "CacheExpirationMinutes": 60}, "DefaultSender": {"FromEmail": "${EMAIL_FROM_ADDRESS}", "FromName": "${EMAIL_FROM_NAME}", "ReplyToEmail": "${EMAIL_REPLY_TO_ADDRESS}", "ReplyToName": "${EMAIL_REPLY_TO_NAME}"}, "Retry": {"MaxAttempts": 3, "DelayMilliseconds": 1000, "ExponentialBackoff": true}}}