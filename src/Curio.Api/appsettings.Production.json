{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning", "Curio.Infrastructure.Services": "Information", "Orleans": "Warning", "Microsoft.Orleans": "Error"}}, "Application": {"Name": "Curio API", "Version": "1.0.0", "Environment": "Production", "Api": {"BaseUrl": "${API_BASE_URL}", "AllowedHosts": ["${ALLOWED_HOSTS}"], "RequestTimeoutSeconds": 30, "MaxRequestBodySize": 10485760, "Cors": {"Enabled": true, "AllowedOrigins": ["${CORS_ALLOWED_ORIGINS}"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowedHeaders": ["Authorization", "Content-Type", "Accept"], "AllowCredentials": true}}, "Security": {"Jwt": {"SecretKey": "${JWT_SECRET_KEY}", "Issuer": "${JWT_ISSUER}", "Audience": "${JWT_AUDIENCE}", "ExpirationMinutes": 60, "RefreshTokenExpirationDays": 7}, "Encryption": {"Key": "${ENCRYPTION_KEY}", "Salt": "${ENCRYPTION_SALT}"}}}, "Database": {"ConnectionString": "${DATABASE_CONNECTION_STRING}", "Host": "${DATABASE_HOST}", "Port": "${DATABASE_PORT:5432}", "Database": "${DATABASE_NAME}", "Username": "${DATABASE_USERNAME}", "Password": "${DATABASE_PASSWORD}", "CommandTimeout": 30, "MaxRetryCount": 3, "EnableSensitiveDataLogging": false}, "Orleans": {"ClusterId": "${ORLEANS_CLUSTER_ID}", "ServiceId": "${ORLEANS_SERVICE_ID}", "Clustering": {"Provider": "AdoNet", "ConnectionString": "${ORLEANS_CLUSTERING_CONNECTION_STRING}", "RefreshPeriod": 30, "DeathVoteExpirationTimeout": 120}, "Storage": {"DefaultProvider": "AdoNet", "ConnectionString": "${ORLEANS_STORAGE_CONNECTION_STRING}", "UseJsonFormat": true}, "Streaming": {"Provider": "Kafka", "ConnectionString": "${KAFKA_BROKERS}"}, "Reminders": {"Provider": "AdoNet", "ConnectionString": "${ORLEANS_REMINDERS_CONNECTION_STRING}"}}, "Kafka": {"BrokerList": ["${KAFKA_BROKERS}"], "ConsumerGroupId": "${KAFKA_CONSUMER_GROUP_ID}", "Topics": ["${KAFKA_TOPICS}"], "SessionTimeoutMs": 30000, "HeartbeatIntervalMs": 3000, "AutoOffsetReset": "earliest", "EnableAutoCommit": true, "AutoCommitIntervalMs": 5000, "MaxPollRecords": 500, "FetchMinBytes": 1, "FetchMaxWaitMs": 500}, "Email": {"Smtp": {"Host": "${SMTP_HOST}", "Port": "${SMTP_PORT:587}", "Username": "${SMTP_USERNAME}", "Password": "${SMTP_PASSWORD}", "EnableSsl": true, "TimeoutSeconds": 30, "UseDefaultCredentials": false}, "Templates": {"TemplatesDirectory": "EmailTemplates", "EnableCaching": true, "CacheExpirationMinutes": 60}, "DefaultSender": {"FromEmail": "${EMAIL_FROM_ADDRESS}", "FromName": "${EMAIL_FROM_NAME}", "ReplyToEmail": "${EMAIL_REPLY_TO_ADDRESS}", "ReplyToName": "${EMAIL_REPLY_TO_NAME}"}, "Retry": {"MaxAttempts": 3, "DelayMilliseconds": 1000, "ExponentialBackoff": true}}}