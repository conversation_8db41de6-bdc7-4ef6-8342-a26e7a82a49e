{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning", "Curio.Infrastructure.Services": "Information", "Orleans": "Warning", "Microsoft.Orleans": "Error"}}, "Application": {"Name": "Curio API", "Version": "1.0.0", "Environment": "Production", "Api": {"BaseUrl": "https://your-api-domain.com", "AllowedHosts": ["your-api-domain.com"], "RequestTimeoutSeconds": 30, "MaxRequestBodySize": 10485760, "Cors": {"Enabled": true, "AllowedOrigins": ["https://your-frontend-domain.com"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowedHeaders": ["Authorization", "Content-Type", "Accept"], "AllowCredentials": true}}, "Security": {"Jwt": {"SecretKey": "CHANGE_ME_PRODUCTION_JWT_SECRET_KEY_MINIMUM_64_CHARACTERS_LONG", "Issuer": "Curio.Production", "Audience": "Curio.Api.Production", "ExpirationMinutes": 60, "RefreshTokenExpirationDays": 7}, "Encryption": {"Key": "CHANGE_ME_PRODUCTION_ENCRYPTION_KEY_32_CHARS", "Salt": "CHANGE_ME_PROD_SALT"}}}, "Database": {"ConnectionString": "Host=your-db-host;Port=5432;Database=orleansdb_prod;Username=orleans_prod;Password=CHANGE_ME_DB_PASSWORD", "Host": "your-db-host", "Port": 5432, "Database": "orleansdb_prod", "Username": "orleans_prod", "Password": "CHANGE_ME_DB_PASSWORD", "CommandTimeout": 30, "MaxRetryCount": 3, "EnableSensitiveDataLogging": false}, "Orleans": {"ClusterId": "curio-cluster-prod", "ServiceId": "curio-service-prod", "Clustering": {"Provider": "AdoNet", "ConnectionString": "Host=your-db-host;Port=5432;Database=orleansdb_prod;Username=orleans_prod;Password=CHANGE_ME_DB_PASSWORD", "RefreshPeriod": 30, "DeathVoteExpirationTimeout": 120}, "Storage": {"DefaultProvider": "AdoNet", "ConnectionString": "Host=your-db-host;Port=5432;Database=orleansdb_prod;Username=orleans_prod;Password=CHANGE_ME_DB_PASSWORD", "UseJsonFormat": true}, "Streaming": {"Provider": "Kafka", "ConnectionString": "your-kafka-brokers:9092"}, "Reminders": {"Provider": "AdoNet", "ConnectionString": "Host=your-db-host;Port=5432;Database=orleansdb_prod;Username=orleans_prod;Password=CHANGE_ME_DB_PASSWORD"}}, "Kafka": {"BrokerList": ["your-kafka-broker1:9092", "your-kafka-broker2:9092"], "ConsumerGroupId": "orleans-event-streams-prod", "Topics": ["domain-events", "verification-events", "user-events"], "SessionTimeoutMs": 30000, "HeartbeatIntervalMs": 3000, "AutoOffsetReset": "earliest", "EnableAutoCommit": true, "AutoCommitIntervalMs": 5000, "MaxPollRecords": 500, "FetchMinBytes": 1, "FetchMaxWaitMs": 500}, "Email": {"Smtp": {"Host": "your-smtp-host.com", "Port": 587, "Username": "CHANGE_ME_SMTP_USERNAME", "Password": "CHANGE_ME_SMTP_PASSWORD", "EnableSsl": true, "TimeoutSeconds": 30, "UseDefaultCredentials": false}, "Templates": {"TemplatesDirectory": "EmailTemplates", "EnableCaching": true, "CacheExpirationMinutes": 60}, "DefaultSender": {"FromEmail": "<EMAIL>", "FromName": "<PERSON><PERSON><PERSON>", "ReplyToEmail": "<EMAIL>", "ReplyToName": "Curio Support"}, "Retry": {"MaxAttempts": 3, "DelayMilliseconds": 1000, "ExponentialBackoff": true}}}