using Microsoft.Extensions.Configuration;
using Orleans;
using Orleans.Configuration;
using Orleans.Hosting;
using Curio.Application.Interfaces;
using Curio.Application.Implementation;
using Curio.Infrastructure;
using Curio.Infrastructure.Configuration;
using DotNetEnv;

// Load .env files in order of priority (for local development)
// 1. Load base .env file
if (File.Exists(".env"))
{
    Env.Load(".env");
}

// 2. Load environment-specific .env file
var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development";
var envFile = $".env.{environment.ToLower()}";
if (File.Exists(envFile))
{
    Env.Load(envFile);
}

// 3. Load local override file (highest priority)
if (File.Exists(".env.local"))
{
    Env.Load(".env.local");
}

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

// Get Orleans configuration
var orleansSettings = builder.Configuration.GetSection(OrleansSettings.SectionName).Get<OrleansSettings>() ?? new OrleansSettings();
var clusteringConnectionString = builder.Configuration.GetOrleansConnectionString("clustering");

// Configure Orleans Client
builder.UseOrleansClient(clientBuilder =>
{
    clientBuilder
        .UseAdoNetClustering(options =>
        {
            options.ConnectionString = clusteringConnectionString;
        })
        .Configure<ClusterOptions>(options =>
        {
            options.ClusterId = orleansSettings.ClusterId;
            options.ServiceId = orleansSettings.ServiceId;
        });
});

// Register application services
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddInfrastructureServices(builder.Configuration);

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

app.UseHttpsRedirection();

app.UseAuthorization();

app.MapControllers();

app.Run();
