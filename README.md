# Curio API - Modern .NET Backend Architecture

A modern backend system built with .NET 9, Orleans, Event Sourcing, CQRS, and Domain-Driven Design (DDD) principles.

## 🏗️ Architecture Overview

This project implements a sophisticated backend architecture following industry best practices:

- **Domain-Driven Design (DDD)** - Clean separation of concerns with domain-centric architecture
- **CQRS (Command Query Responsibility Segregation)** - Separate read and write operations
- **Event Sourcing** - Events as the single source of truth using Orleans JournaledGrain
- **Orleans Virtual Actor Model** - Distributed, scalable grain-based architecture
- **Kafka Streaming** - Event-driven communication and integration
- **PostgreSQL** - Reliable persistence for clustering, state snapshots, and read models

## 📁 Project Structure

```
curio-api/
├── src/
│   ├── Curio.Api/                  # Web API Controllers & Endpoints
│   ├── Curio.Application/          # Application Services & Use Cases
│   ├── Curio.Domain/               # Domain Models & Business Logic
│   ├── Curio.Infrastructure/       # External Integrations & Services
│   ├── Curio.Orleans.Grains/       # Orleans Grain Implementations
│   ├── Curio.Orleans.Interfaces/   # Grain Interface Contracts
│   ├── Curio.Orleans.Silo/         # Orleans Host Configuration
│   └── Curio.Shared/               # Shared Contracts & Events
├── tests/
│   ├── Curio.UnitTests/
│   ├── Curio.IntegrationTests/
│   └── Curio.ArchitectureTests/
└── docs/
    ├── Architecture-Design-Document.md
    ├── API-Design-Standards.md
    └── Event-Storage-Architecture.md
```

## 🚀 Features

### User Registration & Authentication System

- **Email-based Registration** - Users register using email verification codes
- **Passwordless Login** - Secure login using time-limited verification codes
- **Event Sourcing** - All user actions stored as immutable events
- **Real-time Streaming** - Events published to Kafka for integration

### Core Technologies

- **.NET 9** - Latest .NET platform with modern C# features
- **Orleans 9.2.1** - Virtual Actor Model with JournaledGrain event sourcing
- **PostgreSQL** - Clustering, state snapshots, and read model storage
- **Apache Kafka** - Event streaming and external system integration
- **ASP.NET Core** - Modern web API framework

## 🛠️ Development Setup

### Prerequisites

- .NET 9 SDK
- PostgreSQL 13+
- Apache Kafka
- Docker (optional, for containerized dependencies)

### Getting Started

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd curio-api
   ```

2. **Setup local configuration**

   ```bash
   # Copy environment configuration template
   cp .env.example .env

   # Edit .env file with your local settings
   nano .env
   ```

3. **Start dependencies** (using Docker Compose)

   ```bash
   docker compose up -d postgresql kafka
   ```

4. **Setup Orleans database**

   ```bash
   # Create Orleans tables in PostgreSQL
   dotnet run --project src/Curio.Orleans.Silo -- --setup-db
   ```

5. **Start Orleans Silo**

   ```bash
   dotnet run --project src/Curio.Orleans.Silo
   ```

6. **Start Web API**
   ```bash
   dotnet run --project src/Curio.Api
   ```

### Development Commands

```bash
# Build entire solution
dotnet build

# Run all tests
dotnet test

# Run specific test project
dotnet test tests/Curio.UnitTests

# Watch for file changes during development
dotnet watch --project src/Curio.Api
```

## 📊 System Architecture

### Event Sourcing Flow

```
User Action → Command → Grain → Event → JournaledGrain → Kafka Stream
                                   ↓
                            PostgreSQL Snapshot
```

### Orleans Grain Types

- **UserGrain** - Manages user lifecycle and authentication events
- **VerificationGrain** - Handles email verification codes
- **Event Streams** - Publishes domain events to Kafka topics

### Kafka Topics

- `user-events` - User registration, login events
- `verification-events` - Email verification code events
- `domain-events` - General domain events

## 🔌 API Endpoints

### User Management

```
POST /api/users/check-email     # Check if email exists
POST /api/users/send-verification-code  # Send verification code
POST /api/users/register        # Register new user
POST /api/users/login          # Login with verification code
GET  /api/users/{email}        # Get user information
```

### Example Request Flow

```bash
# 1. Check if email exists
curl -X POST /api/users/check-email \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'

# 2. Send verification code
curl -X POST /api/users/send-verification-code \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "purpose": "registration"}'

# 3. Register with code
curl -X POST /api/users/register \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "name": "John Doe", "verificationCode": "123456"}'
```

## 📈 Monitoring & Observability

- **Orleans Dashboard** - Real-time grain monitoring
- **Structured Logging** - JSON-formatted logs with correlation IDs
- **Health Checks** - Built-in health monitoring endpoints
- **Metrics** - Performance counters and custom metrics

## 🧪 Testing Strategy

- **Unit Tests** - Domain logic and business rules
- **Integration Tests** - End-to-end API testing
- **Architecture Tests** - Enforce architectural boundaries
- **Load Tests** - Performance and scalability validation

## 📚 Documentation

- [Architecture Design Document](docs/Architecture-Design-Document.md)
- [API Design Standards](docs/API-Design-Standards.md)
- [Event Storage Architecture](docs/Event-Storage-Architecture.md)
- [Development Guide](CLAUDE.md)

## 🔒 Security Considerations

- **Input Validation** - Comprehensive request validation
- **Rate Limiting** - API throttling and abuse prevention
- **Secrets Management** - Secure configuration handling
- **Authentication** - Time-limited verification codes
- **HTTPS Only** - Encrypted communication in production

## 🌍 Environment Configuration

### Development

- **Clustering**: PostgreSQL ADO.NET
- **Storage**: PostgreSQL for all persistence
- **Streaming**: Local Kafka instance
- **Email**: Console output simulation

### Production

- **Clustering**: PostgreSQL with connection pooling
- **Storage**: PostgreSQL with read replicas
- **Streaming**: Kafka cluster with multiple brokers
- **Email**: SMTP provider integration

## 🤝 Contributing

1. Follow the established architecture patterns
2. Write comprehensive tests for new features
3. Update documentation for API changes
4. Ensure code passes all quality gates
5. Use conventional commit messages

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For questions and support:

- Create an issue in the repository
- Check the documentation in the `docs/` folder
- Review the development guide in `CLAUDE.md`

---

**Built with ❤️ using modern .NET technologies**
