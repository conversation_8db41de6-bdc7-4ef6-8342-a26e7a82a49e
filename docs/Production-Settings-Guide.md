# 生产环境配置指南

## 概述

项目使用本地 `appsettings.Production.json` 文件来管理生产环境的敏感配置信息。这些文件被 `.gitignore` 忽略，不会被提交到版本控制系统。

## 快速开始

### 1. 创建本地生产配置文件

```bash
# API 项目
cp src/Curio.Api/appsettings.Production.json.template src/Curio.Api/appsettings.Production.json

# Orleans Silo 项目
cp src/Curio.Orleans.Silo/appsettings.Production.json.template src/Curio.Orleans.Silo/appsettings.Production.json
```

### 2. 编辑配置文件

编辑刚创建的 `appsettings.Production.json` 文件，替换所有 `CHANGE_ME_*` 占位符：

```bash
# 编辑 API 配置
nano src/Curio.Api/appsettings.Production.json

# 编辑 Silo 配置
nano src/Curio.Orleans.Silo/appsettings.Production.json
```

### 3. 运行生产环境

```bash
# 启动 Orleans Silo
dotnet run --project src/Curio.Orleans.Silo --environment Production

# 启动 API
dotnet run --project src/Curio.Api --environment Production
```

## 需要替换的配置项

### 数据库配置
```json
{
  "Database": {
    "ConnectionString": "Host=your-db-host;Port=5432;Database=orleansdb_prod;Username=orleans_prod;Password=YOUR_ACTUAL_DB_PASSWORD",
    "Host": "your-db-host",
    "Password": "YOUR_ACTUAL_DB_PASSWORD"
  }
}
```

### 安全配置
```json
{
  "Application": {
    "Security": {
      "Jwt": {
        "SecretKey": "YOUR_SUPER_SECURE_JWT_KEY_MINIMUM_64_CHARACTERS_LONG_FOR_PRODUCTION",
        "Issuer": "Curio.Production",
        "Audience": "Curio.Api.Production"
      },
      "Encryption": {
        "Key": "YOUR_ENCRYPTION_KEY_32_CHARACTERS_LONG",
        "Salt": "YOUR_SALT_16_CHARS"
      }
    }
  }
}
```

### 邮件配置
```json
{
  "Email": {
    "Smtp": {
      "Host": "your-smtp-host.com",
      "Username": "YOUR_SMTP_USERNAME",
      "Password": "YOUR_SMTP_PASSWORD"
    },
    "DefaultSender": {
      "FromEmail": "<EMAIL>",
      "ReplyToEmail": "<EMAIL>"
    }
  }
}
```

### API 配置
```json
{
  "Application": {
    "Api": {
      "BaseUrl": "https://your-api-domain.com",
      "AllowedHosts": ["your-api-domain.com"],
      "Cors": {
        "AllowedOrigins": ["https://your-frontend-domain.com"]
      }
    }
  }
}
```

### Kafka 配置
```json
{
  "Kafka": {
    "BrokerList": ["your-kafka-broker1:9092", "your-kafka-broker2:9092"],
    "ConsumerGroupId": "orleans-event-streams-prod"
  }
}
```

## 配置优先级

ASP.NET Core 配置系统的优先级（从高到低）：

1. **命令行参数**
2. **环境变量**
3. **appsettings.Production.json** ← 您的本地生产配置
4. **appsettings.json**

## 安全注意事项

### ✅ 已实现的安全措施

1. **Git 忽略** - `appsettings.Production.json` 已在 `.gitignore` 中被忽略
2. **模板文件** - 提供 `.template` 文件作为配置参考
3. **占位符** - 模板文件使用明显的占位符，避免意外使用

### 🔒 安全最佳实践

1. **强密码策略**
   - JWT 密钥至少 64 字符
   - 数据库密码使用复杂密码
   - 加密密钥使用随机生成的字符串

2. **定期轮换密钥**
   - 特别是在团队成员变动时
   - 建议每季度更换一次生产密钥

3. **访问控制**
   - 限制对生产配置文件的访问
   - 使用专用的生产环境账户

## 团队协作

### 配置文件管理

| 文件 | 是否提交到 Git | 用途 |
|------|----------------|------|
| `appsettings.json` | ✅ 是 | 基础配置模板 |
| `appsettings.Development.json` | ✅ 是 | 开发环境配置 |
| `appsettings.Production.json.template` | ✅ 是 | 生产环境配置模板 |
| `appsettings.Production.json` | ❌ 否 | 本地生产环境配置（包含敏感信息） |

### 新团队成员入职

1. 克隆项目后，复制模板文件：
   ```bash
   cp src/Curio.Api/appsettings.Production.json.template src/Curio.Api/appsettings.Production.json
   cp src/Curio.Orleans.Silo/appsettings.Production.json.template src/Curio.Orleans.Silo/appsettings.Production.json
   ```

2. 从安全渠道获取生产环境的敏感信息

3. 编辑配置文件，替换占位符

### 配置更新流程

1. **添加新配置项**：
   - 更新 `.template` 文件
   - 通知团队成员更新本地配置
   - 更新文档

2. **敏感信息变更**：
   - 通过安全渠道通知团队
   - 更新本地配置文件
   - 验证应用正常运行

## 故障排除

### 1. 配置文件不存在

错误：`Could not find configuration file`

解决：
```bash
# 检查文件是否存在
ls -la src/Curio.Api/appsettings.Production.json

# 如果不存在，从模板创建
cp src/Curio.Api/appsettings.Production.json.template src/Curio.Api/appsettings.Production.json
```

### 2. 配置值未生效

检查：
- 环境变量 `ASPNETCORE_ENVIRONMENT` 是否设置为 `Production`
- 配置文件格式是否正确（JSON 语法）
- 是否有环境变量覆盖了配置文件的值

### 3. 数据库连接失败

检查：
- 数据库服务器是否可访问
- 连接字符串是否正确
- 用户名密码是否正确
- 防火墙设置

## 部署到真实生产环境

对于真实的生产环境部署，建议：

1. **使用环境变量** - 通过容器编排系统注入敏感配置
2. **密钥管理服务** - 使用 Azure Key Vault、AWS Secrets Manager 等
3. **配置中心** - 使用 Azure App Configuration、Consul 等
4. **CI/CD 集成** - 在部署流水线中安全地注入配置

## 示例配置

### 完整的生产配置示例

```json
{
  "Database": {
    "ConnectionString": "Host=prod-db.company.com;Port=5432;Database=curio_prod;Username=curio_user;Password=SecurePassword123!"
  },
  "Application": {
    "Api": {
      "BaseUrl": "https://api.curio.company.com",
      "AllowedHosts": ["api.curio.company.com"],
      "Cors": {
        "AllowedOrigins": ["https://app.curio.company.com"]
      }
    },
    "Security": {
      "Jwt": {
        "SecretKey": "SuperSecureJwtKeyForProductionUseMinimum64CharactersLongForSecurity123!",
        "Issuer": "Curio.Production",
        "Audience": "Curio.Api.Production"
      }
    }
  },
  "Email": {
    "Smtp": {
      "Host": "smtp.sendgrid.net",
      "Username": "apikey",
      "Password": "SG.your-sendgrid-api-key"
    },
    "DefaultSender": {
      "FromEmail": "<EMAIL>",
      "FromName": "Curio"
    }
  }
}
```

## 总结

这种方案的优势：

- ✅ **简单直接** - 直接使用 ASP.NET Core 的配置系统
- ✅ **安全可靠** - 敏感信息不会被提交到版本控制
- ✅ **易于管理** - 标准的 JSON 配置文件，IDE 支持良好
- ✅ **团队友好** - 提供模板文件，便于团队协作
- ✅ **环境隔离** - 开发和生产配置完全分离
