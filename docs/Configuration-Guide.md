# Curio API 配置指南

本文档描述了 Curio API 项目的配置结构和使用方法。

## 快速开始

### 本地开发设置

1. **复制环境配置文件**

   ```bash
   cp .env.example .env
   ```

2. **编辑 .env 文件**

   ```bash
   # 修改数据库密码
   DATABASE_PASSWORD=your_actual_password

   # 设置邮件配置
   SMTP_USERNAME=<EMAIL>
   SMTP_PASSWORD=your-app-password
   EMAIL_FROM_ADDRESS=<EMAIL>

   # 设置JWT密钥（生产环境请使用更安全的密钥）
   JWT_SECRET_KEY=your-super-secret-jwt-key-minimum-32-characters-long
   ```

3. **启动应用**

   ```bash
   # 启动 Orleans Silo
   dotnet run --project src/Curio.Orleans.Silo

   # 启动 API（新终端窗口）
   dotnet run --project src/Curio.Api
   ```

### 生产环境设置

生产环境建议使用系统环境变量或容器编排系统的密钥管理，而不是 .env 文件。

详细设置请参考 [本地环境配置指南](Local-Environment-Setup.md)。

## 配置文件结构

项目使用分层配置文件结构，支持不同环境的配置：

### API 项目配置文件

- `src/Curio.Api/appsettings.json` - 基础配置模板
- `src/Curio.Api/appsettings.Development.json` - 开发环境配置
- `src/Curio.Api/appsettings.Production.json` - 生产环境配置

### Orleans Silo 项目配置文件

- `src/Curio.Orleans.Silo/appsettings.json` - 基础配置模板
- `src/Curio.Orleans.Silo/appsettings.Development.json` - 开发环境配置
- `src/Curio.Orleans.Silo/appsettings.Production.json` - 生产环境配置

## 配置节说明

### 1. Application 配置

```json
{
  "Application": {
    "Name": "Curio API",
    "Version": "1.0.0",
    "Environment": "Development",
    "Api": {
      "BaseUrl": "https://localhost:7274",
      "AllowedHosts": ["*"],
      "RequestTimeoutSeconds": 30,
      "MaxRequestBodySize": 10485760,
      "Cors": {
        "Enabled": true,
        "AllowedOrigins": ["http://localhost:3000"],
        "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        "AllowedHeaders": ["*"],
        "AllowCredentials": true
      }
    },
    "Security": {
      "Jwt": {
        "SecretKey": "your-secret-key",
        "Issuer": "Curio",
        "Audience": "Curio.Api",
        "ExpirationMinutes": 60,
        "RefreshTokenExpirationDays": 7
      },
      "Encryption": {
        "Key": "encryption-key",
        "Salt": "encryption-salt"
      }
    }
  }
}
```

### 2. Database 配置

```json
{
  "Database": {
    "ConnectionString": "Host=localhost;Port=5432;Database=orleansdb;Username=orleans;Password=**********",
    "Host": "localhost",
    "Port": 5432,
    "Database": "orleansdb",
    "Username": "orleans",
    "Password": "**********",
    "CommandTimeout": 30,
    "MaxRetryCount": 3,
    "EnableSensitiveDataLogging": false
  }
}
```

### 3. Orleans 配置

```json
{
  "Orleans": {
    "ClusterId": "curio-cluster",
    "ServiceId": "curio-service",
    "Clustering": {
      "Provider": "AdoNet",
      "ConnectionString": "Host=localhost;Port=5432;Database=orleansdb;Username=orleans;Password=**********",
      "RefreshPeriod": 30,
      "DeathVoteExpirationTimeout": 120
    },
    "Storage": {
      "DefaultProvider": "AdoNet",
      "ConnectionString": "Host=localhost;Port=5432;Database=orleansdb;Username=orleans;Password=**********",
      "UseJsonFormat": true
    },
    "Streaming": {
      "Provider": "Kafka",
      "ConnectionString": "localhost:9092"
    },
    "Reminders": {
      "Provider": "AdoNet",
      "ConnectionString": "Host=localhost;Port=5432;Database=orleansdb;Username=orleans;Password=**********"
    }
  }
}
```

### 4. Kafka 配置

```json
{
  "Kafka": {
    "BrokerList": ["localhost:9092"],
    "ConsumerGroupId": "orleans-event-streams",
    "Topics": ["domain-events", "verification-events", "user-events"],
    "SessionTimeoutMs": 30000,
    "HeartbeatIntervalMs": 3000,
    "AutoOffsetReset": "earliest",
    "EnableAutoCommit": true,
    "AutoCommitIntervalMs": 5000,
    "MaxPollRecords": 500,
    "FetchMinBytes": 1,
    "FetchMaxWaitMs": 500
  }
}
```

### 5. Email 配置

```json
{
  "Email": {
    "Smtp": {
      "Host": "smtp.gmail.com",
      "Port": 587,
      "Username": "<EMAIL>",
      "Password": "your-app-password",
      "EnableSsl": true,
      "TimeoutSeconds": 30,
      "UseDefaultCredentials": false
    },
    "Templates": {
      "TemplatesDirectory": "EmailTemplates",
      "EnableCaching": true,
      "CacheExpirationMinutes": 60
    },
    "DefaultSender": {
      "FromEmail": "<EMAIL>",
      "FromName": "Curio",
      "ReplyToEmail": "<EMAIL>",
      "ReplyToName": "Curio Support"
    },
    "Retry": {
      "MaxAttempts": 3,
      "DelayMilliseconds": 1000,
      "ExponentialBackoff": true
    }
  }
}
```

## 环境变量和 .env 文件支持

项目支持两种方式管理环境配置：

### 1. .env 文件（推荐用于本地开发）

在项目根目录创建 `.env` 文件来管理本地开发配置：

```bash
# 复制示例文件
cp .env.example .env

# 编辑配置
nano .env
```

**优势**：

- 不会被提交到版本控制系统（已在 .gitignore 中忽略）
- 便于本地开发和测试
- 支持团队成员使用不同的本地配置

### 2. 系统环境变量（推荐用于生产环境）

生产环境建议使用系统环境变量或容器编排系统的密钥管理。

**配置优先级**（从高到低）：

1. 系统环境变量
2. .env 文件
3. appsettings.{Environment}.json
4. appsettings.json

### 支持的环境变量：

### 数据库相关

- `DATABASE_CONNECTION_STRING` - 完整的数据库连接字符串
- `DATABASE_HOST` - 数据库主机地址
- `DATABASE_PORT` - 数据库端口（默认：5432）
- `DATABASE_NAME` - 数据库名称
- `DATABASE_USERNAME` - 数据库用户名
- `DATABASE_PASSWORD` - 数据库密码

### Orleans 相关

- `ORLEANS_CLUSTER_ID` - Orleans 集群 ID
- `ORLEANS_SERVICE_ID` - Orleans 服务 ID
- `ORLEANS_CLUSTERING_CONNECTION_STRING` - Orleans 集群连接字符串
- `ORLEANS_STORAGE_CONNECTION_STRING` - Orleans 存储连接字符串
- `ORLEANS_REMINDERS_CONNECTION_STRING` - Orleans 提醒服务连接字符串

### Kafka 相关

- `KAFKA_BROKERS` - Kafka 代理地址列表
- `KAFKA_CONSUMER_GROUP_ID` - Kafka 消费者组 ID
- `KAFKA_TOPICS` - Kafka 主题列表

### API 相关

- `API_BASE_URL` - API 基础 URL
- `ALLOWED_HOSTS` - 允许的主机列表
- `CORS_ALLOWED_ORIGINS` - CORS 允许的源列表

### 安全相关

- `JWT_SECRET_KEY` - JWT 密钥
- `JWT_ISSUER` - JWT 发行者
- `JWT_AUDIENCE` - JWT 受众
- `ENCRYPTION_KEY` - 加密密钥
- `ENCRYPTION_SALT` - 加密盐值

### 邮件相关

- `SMTP_HOST` - SMTP 服务器地址
- `SMTP_PORT` - SMTP 端口（默认：587）
- `SMTP_USERNAME` - SMTP 用户名
- `SMTP_PASSWORD` - SMTP 密码
- `EMAIL_FROM_ADDRESS` - 发件人邮箱
- `EMAIL_FROM_NAME` - 发件人姓名
- `EMAIL_REPLY_TO_ADDRESS` - 回复邮箱
- `EMAIL_REPLY_TO_NAME` - 回复姓名

## 配置类

项目定义了强类型的配置类，位于 `src/Curio.Infrastructure/Configuration/` 目录：

- `DatabaseSettings` - 数据库配置
- `KafkaSettings` - Kafka 配置
- `OrleansSettings` - Orleans 配置
- `ApplicationSettings` - 应用程序配置
- `EmailSettings` - 邮件配置

## 使用示例

### 在代码中使用配置

```csharp
// 注入配置
public class MyService
{
    private readonly DatabaseSettings _databaseSettings;

    public MyService(IOptions<DatabaseSettings> databaseOptions)
    {
        _databaseSettings = databaseOptions.Value;
    }

    public void DoSomething()
    {
        var connectionString = _databaseSettings.ConnectionString;
        // 使用配置...
    }
}
```

### 获取连接字符串的便捷方法

```csharp
// 获取数据库连接字符串
var connectionString = configuration.GetDatabaseConnectionString();

// 获取 Orleans 连接字符串
var clusteringConnectionString = configuration.GetOrleansConnectionString("clustering");
var storageConnectionString = configuration.GetOrleansConnectionString("storage");

// 获取 Kafka 代理列表
var kafkaBrokers = configuration.GetKafkaBrokers();
var kafkaBrokersString = configuration.GetKafkaBrokersString();
```

## 部署注意事项

1. **开发环境**：直接使用 `appsettings.Development.json` 中的配置
2. **生产环境**：设置相应的环境变量，系统会自动替换配置文件中的占位符
3. **敏感信息**：生产环境的敏感信息（如密码、密钥）应通过环境变量或密钥管理服务提供
4. **配置验证**：启动时会验证必要的配置项是否存在

## 配置优先级

ASP.NET Core 配置系统的优先级（从高到低）：

1. 命令行参数
2. 环境变量
3. appsettings.{Environment}.json
4. appsettings.json
5. 默认值
