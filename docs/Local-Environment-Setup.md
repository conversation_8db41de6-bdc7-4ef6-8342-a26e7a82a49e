# 本地环境配置指南

## 概述

本项目支持使用 `.env` 文件进行本地开发配置，这样可以避免在代码中硬编码敏感信息，同时不会将这些信息提交到版本控制系统。

## 快速开始

### 1. 复制示例配置文件

您可以根据需要选择不同的配置方式：

**选项 A：使用通用配置**

```bash
cp .env.example .env
```

**选项 B：使用环境特定配置**

```bash
# 开发环境配置
cp .env.development.example .env.development

# 生产环境配置（如果需要本地测试生产配置）
cp .env.production.example .env.production

# 个人本地覆盖配置
cp .env.local.example .env.local
```

### 2. 编辑 .env 文件

打开 `.env` 文件，根据你的本地环境修改相应的配置值：

```bash
# 数据库配置
DATABASE_CONNECTION_STRING=Host=localhost;Port=5432;Database=orleansdb;Username=orleans;Password=your_password

# JWT 密钥（生产环境请使用更安全的密钥）
JWT_SECRET_KEY=your-super-secret-jwt-key-minimum-32-characters-long-for-security

# 邮件配置
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM_ADDRESS=<EMAIL>
```

### 3. 启动应用

```bash
# 启动 Orleans Silo
dotnet run --project src/Curio.Orleans.Silo

# 启动 API
dotnet run --project src/Curio.Api
```

## 环境特定的 .env 文件

项目支持多个 .env 文件，按以下优先级加载（后加载的会覆盖先加载的）：

### 加载顺序

1. **`.env`** - 基础配置文件
2. **`.env.{environment}`** - 环境特定配置（如 `.env.development`、`.env.production`）
3. **`.env.local`** - 本地覆盖配置（最高优先级）

### 使用场景

| 文件               | 用途                           | 示例                     |
| ------------------ | ------------------------------ | ------------------------ |
| `.env`             | 通用配置，所有环境共享的默认值 | 基础端口、通用设置       |
| `.env.development` | 开发环境特定配置               | 开发数据库、调试设置     |
| `.env.production`  | 生产环境特定配置               | 生产数据库、性能优化设置 |
| `.env.local`       | 个人本地覆盖配置               | 个人邮箱、特殊端口设置   |

### 示例配置

**开发环境** (`dotnet run` 默认)：

```bash
# 会依次加载：
# 1. .env
# 2. .env.development
# 3. .env.local
```

**生产环境** (`dotnet run --environment Production`)：

```bash
# 会依次加载：
# 1. .env
# 2. .env.production
# 3. .env.local
```

## 配置优先级

完整的配置系统优先级（从高到低）：

1. **命令行参数**
2. **系统环境变量**
3. **`.env.local`** 文件
4. **`.env.{environment}`** 文件（如 .env.development）
5. **`.env`** 文件
6. **appsettings.{Environment}.json** - 环境特定配置文件
7. **appsettings.json** - 基础配置文件

## 环境变量说明

### 数据库配置

| 变量名                       | 说明                 | 示例值                                                                           |
| ---------------------------- | -------------------- | -------------------------------------------------------------------------------- |
| `DATABASE_CONNECTION_STRING` | 完整数据库连接字符串 | `Host=localhost;Port=5432;Database=orleansdb;Username=orleans;Password=password` |
| `DATABASE_HOST`              | 数据库主机           | `localhost`                                                                      |
| `DATABASE_PORT`              | 数据库端口           | `5432`                                                                           |
| `DATABASE_NAME`              | 数据库名称           | `orleansdb`                                                                      |
| `DATABASE_USERNAME`          | 数据库用户名         | `orleans`                                                                        |
| `DATABASE_PASSWORD`          | 数据库密码           | `your_password`                                                                  |

### Orleans 配置

| 变量名                                 | 说明               | 示例值                |
| -------------------------------------- | ------------------ | --------------------- |
| `ORLEANS_CLUSTER_ID`                   | Orleans 集群 ID    | `curio-cluster-local` |
| `ORLEANS_SERVICE_ID`                   | Orleans 服务 ID    | `curio-service-local` |
| `ORLEANS_CLUSTERING_CONNECTION_STRING` | 集群连接字符串     | 同数据库连接字符串    |
| `ORLEANS_STORAGE_CONNECTION_STRING`    | 存储连接字符串     | 同数据库连接字符串    |
| `ORLEANS_REMINDERS_CONNECTION_STRING`  | 提醒服务连接字符串 | 同数据库连接字符串    |

### Kafka 配置

| 变量名                    | 说明                 | 示例值                                          |
| ------------------------- | -------------------- | ----------------------------------------------- |
| `KAFKA_BROKERS`           | Kafka 代理地址       | `localhost:9092`                                |
| `KAFKA_CONSUMER_GROUP_ID` | 消费者组 ID          | `orleans-event-streams-local`                   |
| `KAFKA_TOPICS`            | 主题列表（逗号分隔） | `domain-events,verification-events,user-events` |

### 安全配置

| 变量名            | 说明                         | 示例值                                                 |
| ----------------- | ---------------------------- | ------------------------------------------------------ |
| `JWT_SECRET_KEY`  | JWT 签名密钥（至少 32 字符） | `your-super-secret-jwt-key-minimum-32-characters-long` |
| `JWT_ISSUER`      | JWT 发行者                   | `Curio.Local`                                          |
| `JWT_AUDIENCE`    | JWT 受众                     | `Curio.Api.Local`                                      |
| `ENCRYPTION_KEY`  | 加密密钥                     | `your-encryption-key-32-characters`                    |
| `ENCRYPTION_SALT` | 加密盐值                     | `your-salt-16-chars`                                   |

### 邮件配置

| 变量名               | 说明        | 示例值                 |
| -------------------- | ----------- | ---------------------- |
| `SMTP_HOST`          | SMTP 服务器 | `smtp.gmail.com`       |
| `SMTP_PORT`          | SMTP 端口   | `587`                  |
| `SMTP_USERNAME`      | SMTP 用户名 | `<EMAIL>` |
| `SMTP_PASSWORD`      | SMTP 密码   | `your-app-password`    |
| `EMAIL_FROM_ADDRESS` | 发件人邮箱  | `<EMAIL>` |
| `EMAIL_FROM_NAME`    | 发件人姓名  | `Curio Local`          |

### API 配置

| 变量名                 | 说明                      | 示例值                                         |
| ---------------------- | ------------------------- | ---------------------------------------------- |
| `API_BASE_URL`         | API 基础 URL              | `https://localhost:7274`                       |
| `ALLOWED_HOSTS`        | 允许的主机                | `*`                                            |
| `CORS_ALLOWED_ORIGINS` | CORS 允许的源（逗号分隔） | `http://localhost:3000,https://localhost:7274` |

## 安全注意事项

1. **永远不要提交 .env 文件到版本控制系统**

   - `.env` 文件已经在 `.gitignore` 中被忽略
   - 确保不要手动添加到 git

2. **使用强密码和密钥**

   - JWT 密钥至少 32 字符
   - 数据库密码使用复杂密码
   - 加密密钥使用随机生成的字符串

3. **定期更换密钥**
   - 特别是在团队成员变动时
   - 生产环境密钥应该定期轮换

## 故障排除

### 1. .env 文件不生效

确认：

- `.env` 文件在项目根目录
- 文件格式正确（`KEY=VALUE`，无空格）
- 应用程序已重启

### 2. 数据库连接失败

检查：

- 数据库服务是否运行
- 连接字符串是否正确
- 用户名密码是否正确
- 防火墙设置

### 3. Kafka 连接失败

检查：

- Kafka 服务是否运行
- 端口是否正确
- 主题是否存在

## 生产环境部署

生产环境建议：

1. 使用容器编排系统的密钥管理（如 Kubernetes Secrets）
2. 使用云服务商的密钥管理服务（如 Azure Key Vault、AWS Secrets Manager）
3. 通过环境变量注入配置，而不是 .env 文件

## 示例 .env 文件

参考项目根目录的 `.env.example` 文件，它包含了所有可配置的环境变量及其说明。
