# 配置管理总结

## 概述

项目配置已经重构完成，现在使用简洁的 appsettings 文件管理方式，支持环境分离和敏感信息保护。

## 配置文件结构

### 提交到版本控制的文件
```
src/Curio.Api/
├── appsettings.json                          # 基础配置
├── appsettings.Development.json              # 开发环境配置
└── appsettings.Production.json.template      # 生产环境配置模板

src/Curio.Orleans.Silo/
├── appsettings.json                          # 基础配置
├── appsettings.Development.json              # 开发环境配置
└── appsettings.Production.json.template      # 生产环境配置模板
```

### 本地文件（不提交到版本控制）
```
src/Curio.Api/
└── appsettings.Production.json               # 本地生产环境配置（包含敏感信息）

src/Curio.Orleans.Silo/
└── appsettings.Production.json               # 本地生产环境配置（包含敏感信息）
```

## 快速开始

### 1. 开发环境（默认）
```bash
# 直接运行，使用 appsettings.Development.json
dotnet run --project src/Curio.Api
dotnet run --project src/Curio.Orleans.Silo
```

### 2. 本地测试生产配置
```bash
# 1. 创建本地生产配置文件
cp src/Curio.Api/appsettings.Production.json.template src/Curio.Api/appsettings.Production.json
cp src/Curio.Orleans.Silo/appsettings.Production.json.template src/Curio.Orleans.Silo/appsettings.Production.json

# 2. 编辑配置文件，替换 CHANGE_ME_* 占位符
nano src/Curio.Api/appsettings.Production.json
nano src/Curio.Orleans.Silo/appsettings.Production.json

# 3. 运行生产环境
dotnet run --project src/Curio.Api --environment Production
dotnet run --project src/Curio.Orleans.Silo --environment Production
```

## 配置节说明

### Application 配置
- API 基础设置（URL、CORS、超时等）
- 安全配置（JWT、加密）

### Database 配置
- 数据库连接信息
- 连接池设置
- 日志配置

### Orleans 配置
- 集群配置
- 存储配置
- 流处理配置
- 提醒服务配置

### Kafka 配置
- 代理地址
- 消费者组
- 主题配置

### Email 配置
- SMTP 设置
- 模板配置
- 发件人信息

## 安全特性

### ✅ 已实现
- `appsettings.Production.json` 在 `.gitignore` 中被忽略
- 提供 `.template` 文件作为配置参考
- 使用明显的占位符避免意外使用默认值
- 强类型配置类，编译时检查

### 🔒 最佳实践
- 生产环境使用强密码和长密钥
- 定期轮换敏感信息
- 通过安全渠道分发生产配置
- 限制对配置文件的访问权限

## 配置优先级

ASP.NET Core 配置系统优先级（从高到低）：
1. 命令行参数
2. 环境变量
3. appsettings.{Environment}.json
4. appsettings.json

## 团队协作

### 新成员入职
1. 克隆项目
2. 复制 `.template` 文件到对应的 `.json` 文件
3. 从安全渠道获取敏感信息并填入配置文件
4. 验证应用正常运行

### 配置更新
1. 更新 `.template` 文件
2. 通知团队成员更新本地配置
3. 更新相关文档

## 部署建议

### 开发环境
- 使用 `appsettings.Development.json`
- 可以包含简单的测试密钥

### 生产环境
- 使用环境变量或密钥管理服务
- 不要在服务器上存储明文配置文件
- 考虑使用 Azure Key Vault、AWS Secrets Manager 等

## 故障排除

### 配置文件不存在
```bash
# 检查文件
ls -la src/Curio.Api/appsettings.Production.json

# 从模板创建
cp src/Curio.Api/appsettings.Production.json.template src/Curio.Api/appsettings.Production.json
```

### 配置值未生效
- 检查环境变量 `ASPNETCORE_ENVIRONMENT`
- 验证 JSON 格式是否正确
- 确认没有环境变量覆盖配置文件值

## 优势总结

✅ **简单直接** - 使用标准的 ASP.NET Core 配置系统
✅ **安全可靠** - 敏感信息不会被提交到版本控制
✅ **易于管理** - 标准 JSON 格式，IDE 支持良好
✅ **团队友好** - 提供模板文件，便于协作
✅ **环境隔离** - 开发和生产配置完全分离
✅ **类型安全** - 强类型配置类，避免运行时错误

这种配置方案既保持了简洁性，又确保了安全性和可维护性。
