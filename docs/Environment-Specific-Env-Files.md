# 环境特定 .env 文件支持

## 概述

现在项目支持环境特定的 `.env` 文件，可以根据不同的运行环境自动加载相应的配置文件。

## 支持的 .env 文件

| 文件名 | 用途 | 加载时机 |
|--------|------|----------|
| `.env` | 基础配置，所有环境共享 | 总是加载 |
| `.env.development` | 开发环境特定配置 | `ASPNETCORE_ENVIRONMENT=Development` 时加载 |
| `.env.production` | 生产环境特定配置 | `ASPNETCORE_ENVIRONMENT=Production` 时加载 |
| `.env.staging` | 测试环境特定配置 | `ASPNETCORE_ENVIRONMENT=Staging` 时加载 |
| `.env.local` | 个人本地覆盖配置 | 总是加载（最高优先级） |

## 加载顺序和优先级

配置按以下顺序加载，后加载的会覆盖先加载的：

1. **`.env`** - 基础配置
2. **`.env.{environment}`** - 环境特定配置
3. **`.env.local`** - 本地覆盖配置

### 示例

**开发环境** (`dotnet run` 默认)：
```bash
# 加载顺序：
1. .env                    # 基础配置
2. .env.development        # 开发环境配置
3. .env.local             # 个人覆盖配置
```

**生产环境** (`dotnet run --environment Production`)：
```bash
# 加载顺序：
1. .env                    # 基础配置
2. .env.production         # 生产环境配置
3. .env.local             # 个人覆盖配置
```

## 使用方法

### 1. 复制示例文件

```bash
# 基础配置
cp .env.example .env

# 开发环境配置
cp .env.development.example .env.development

# 生产环境配置（如果需要本地测试）
cp .env.production.example .env.production

# 个人覆盖配置
cp .env.local.example .env.local
```

### 2. 编辑配置文件

根据需要编辑相应的配置文件：

**`.env`** - 通用配置：
```bash
# 所有环境共享的基础配置
KAFKA_BROKERS=localhost:9092
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
```

**`.env.development`** - 开发环境：
```bash
# 开发环境特定配置
DATABASE_NAME=orleansdb_dev
ORLEANS_CLUSTER_ID=curio-cluster-dev
JWT_SECRET_KEY=development-jwt-key-32-chars-minimum
DATABASE_ENABLE_SENSITIVE_DATA_LOGGING=true
```

**`.env.production`** - 生产环境：
```bash
# 生产环境特定配置
DATABASE_NAME=orleansdb_prod
ORLEANS_CLUSTER_ID=curio-cluster-prod
JWT_SECRET_KEY=production-super-secure-jwt-key-64-chars-minimum
DATABASE_ENABLE_SENSITIVE_DATA_LOGGING=false
```

**`.env.local`** - 个人覆盖：
```bash
# 个人开发偏好设置
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=my-personal-app-password
EMAIL_FROM_ADDRESS=<EMAIL>
DATABASE_PORT=5433  # 如果你使用不同的端口
```

### 3. 运行应用

```bash
# 开发环境（默认）
dotnet run --project src/Curio.Api

# 生产环境
dotnet run --project src/Curio.Api --environment Production

# 测试环境
dotnet run --project src/Curio.Api --environment Staging
```

## 配置策略建议

### 团队协作

1. **`.env`** - 提交示例文件 (`.env.example`)，包含团队共享的基础配置
2. **`.env.development`** - 提交示例文件，包含开发环境的标准配置
3. **`.env.production`** - 提交示例文件，但不包含真实的生产密钥
4. **`.env.local`** - 永远不提交，用于个人覆盖设置

### 安全考虑

1. **开发环境** - 可以使用相对简单的密钥，便于开发调试
2. **生产环境** - 必须使用强密钥，通过安全渠道分发
3. **个人配置** - 包含个人邮箱等敏感信息，不应共享

### 配置分离

```bash
# .env - 基础配置
API_BASE_URL=https://localhost:7274
CORS_ALLOWED_ORIGINS=http://localhost:3000

# .env.development - 开发配置
DATABASE_PASSWORD=simple_dev_password
JWT_SECRET_KEY=dev-key-32-chars-minimum-length
EMAIL_CACHE_EXPIRATION_MINUTES=1

# .env.production - 生产配置
DATABASE_PASSWORD=complex_prod_password_123!
JWT_SECRET_KEY=prod-super-secure-key-64-chars-minimum-length-for-security
EMAIL_CACHE_EXPIRATION_MINUTES=60

# .env.local - 个人配置
SMTP_USERNAME=<EMAIL>
DATABASE_PORT=5433
```

## Git 忽略规则

所有 `.env` 文件都已在 `.gitignore` 中被忽略：

```gitignore
# Environment files
.env
.env.local
.env.development
.env.production
.env.staging
```

## 故障排除

### 1. 配置不生效

检查：
- 文件名是否正确（注意大小写）
- 文件是否在项目根目录
- 环境变量 `ASPNETCORE_ENVIRONMENT` 是否正确设置
- 应用是否已重启

### 2. 环境检测

在代码中添加日志来检查当前环境：

```csharp
var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development";
Console.WriteLine($"Current environment: {environment}");
```

### 3. 配置优先级问题

记住配置优先级：
1. 系统环境变量（最高）
2. `.env.local`
3. `.env.{environment}`
4. `.env`
5. appsettings 文件（最低）

## 最佳实践

1. **使用环境特定文件** - 为不同环境创建专门的配置文件
2. **保持基础配置简洁** - `.env` 只包含真正通用的配置
3. **使用 .env.local 进行个人覆盖** - 避免修改团队共享的配置文件
4. **定期更新示例文件** - 当添加新配置项时，更新相应的 `.example` 文件
5. **文档化配置变更** - 在团队中沟通配置文件的变更

## 总结

通过环境特定的 `.env` 文件支持，现在可以：

- ✅ 为不同环境使用不同的配置
- ✅ 保持团队配置的一致性
- ✅ 允许个人进行本地覆盖
- ✅ 简化环境切换过程
- ✅ 提高配置管理的灵活性

这种方式既保持了配置的灵活性，又确保了团队协作的便利性和安全性。
